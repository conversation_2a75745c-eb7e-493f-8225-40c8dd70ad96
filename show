FROM python:3.9-slim

WORKDIR /app

# Cài đặt dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY *.py .

# <PERSON><PERSON><PERSON> hình AWS credentials sẽ được mount từ máy host

# Port mặc định cho Streamlit
EXPOSE 8080

# Tạo user không phải root
RUN addgroup --system appgroup && \
    adduser --system --group appuser && \
    chown -R appuser:appgroup /app

# Chuyển sang user không phải root
USER appuser

# Sửa lỗi port và đường dẫn
ENV PATH="/usr/local/bin:${PATH}"
CMD ["streamlit", "run", "app.py", "--server.address=0.0.0.0", "--server.port=8080"]# image_understanding
streamlit run image_understanding_app.py --server.port 8080 --server.enab
leXsrfProtection=false

# Docker Run
docker run -dt -p 80:8080 -v ~/.aws:/root/.aws --name image-understanding-app-tokyo image-understanding-app-tokyoimport streamlit as st
import logging
import json
import pandas as pd
from rag_processor import RAGProcessor
from data_ops import get_db_connection, get_pending_review_items, generate_presigned_url

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    layout="wide",
    page_title="RAG Data Management",
    page_icon="📊"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #2E86AB;
        padding: 20px 0;
    }
    .upload-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
    .result-success {
        background-color: #d4edda;
        color: #155724;
        padding: 15px;
        border-radius: 5px;
        border-left: 5px solid #28a745;
    }
    .result-error {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        border-left: 5px solid #dc3545;
    }
    .result-warning {
        background-color: #fff3cd;
        color: #856404;
        padding: 15px;
        border-radius: 5px;
        border-left: 5px solid #ffc107;
    }
    .stats-container {
        display: flex;
        justify-content: space-around;
        margin: 20px 0;
    }
    .stat-box {
        text-align: center;
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 8px;
        min-width: 120px;
    }
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #2E86AB;
    }
    .review-item {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
        border: 1px solid #dee2e6;
    }
    .review-image {
        text-align: center;
        padding: 10px;
    }
    .review-result {
        background-color: #ffffff;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ced4da;
    }
</style>
""", unsafe_allow_html=True)

# Main header
st.markdown('<h1 class="main-header">📊 RAG Data Management</h1>', unsafe_allow_html=True)
st.markdown('<p style="text-align: center; color: #666;">Data Ingestion and Review System</p>', unsafe_allow_html=True)

# Create tabs
tab1, tab2 = st.tabs(["📊 RAG Data Ingestion", "🔍 Review"])

# Tab 1: RAG Data Ingestion
with tab1:
    st.markdown("### Upload CSV and Images for Processing")

    # Layout: Two columns for upload, full width for results
    col1, col2 = st.columns([1, 1])

    # Upload CSV section
    with col1:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📄 Upload CSV File")
        st.markdown("*Required columns: image_name, description*")

        uploaded_csv = st.file_uploader(
            "Choose CSV file",
            type=["csv"],
            key="csv_upload",
            help="CSV file containing image descriptions with columns: image_name, value"
        )

        if uploaded_csv:
            st.success(f"✅ CSV uploaded: {uploaded_csv.name}")
            st.info(f"File size: {uploaded_csv.size:,} bytes")

        st.markdown('</div>', unsafe_allow_html=True)

    # Upload Images section
    with col2:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("🖼️ Upload Images")
        st.markdown("*Supported formats: PNG, JPG, JPEG*")

        uploaded_images = st.file_uploader(
            "Choose image files",
            type=["png", "jpg", "jpeg"],
            accept_multiple_files=True,
            key="images_upload",
            help="Image files named as <number>.jpg matching CSV image_name column"
        )

        if uploaded_images:
            st.success(f"✅ Images uploaded: {len(uploaded_images)} files")

            # # Show image preview
            # if len(uploaded_images) <= 5:  # Only show preview for small sets
            #     st.markdown("**Preview:**")
            #     cols = st.columns(min(len(uploaded_images), 5))
            #     for i, img in enumerate(uploaded_images[:5]):
            #         with cols[i]:
            #             st.image(img, caption=img.name, width=100)
            # else:
            #     st.info(f"Too many images to preview. Total: {len(uploaded_images)}")

        st.markdown('</div>', unsafe_allow_html=True)

    # Processing section
    st.markdown("---")

    # Process button
    col_btn1, col_btn2, col_btn3 = st.columns([1, 1, 1])
    with col_btn2:
        process_button = st.button(
            "🚀 Start Processing",
            type="primary",
            use_container_width=True,
            disabled=not (uploaded_csv and uploaded_images)
        )

    # Results section
    if process_button:
        if not uploaded_csv or not uploaded_images:
            st.error("❌ Please upload both CSV file and images before processing")
        else:
            # Initialize processor
            processor = RAGProcessor()

            # Show processing status
            with st.spinner("🔄 Processing RAG data ingestion..."):
                # Run workflow
                results = processor.run_full_workflow(uploaded_csv, uploaded_images)

            st.markdown("---")
            st.subheader("📊 Processing Results")
        
            # Results statistics
            if results['total_records'] > 0:
                st.markdown('<div class="stats-container">', unsafe_allow_html=True)

                col_stat1, col_stat2, col_stat3, col_stat4, col_stat5 = st.columns(5)

                with col_stat1:
                    st.markdown(f'''
                    <div class="stat-box">
                        <div class="stat-number">{results['total_records']}</div>
                        <div>Total Records</div>
                    </div>
                    ''', unsafe_allow_html=True)

                with col_stat2:
                    st.markdown(f'''
                    <div class="stat-box">
                        <div class="stat-number" style="color: #28a745;">{results['successful']}</div>
                        <div>Successful</div>
                    </div>
                    ''', unsafe_allow_html=True)

                with col_stat3:
                    st.markdown(f'''
                    <div class="stat-box">
                        <div class="stat-number" style="color: #ffc107;">{results.get('skipped', 0)}</div>
                        <div>Skipped</div>
                    </div>
                    ''', unsafe_allow_html=True)

                with col_stat4:
                    st.markdown(f'''
                    <div class="stat-box">
                        <div class="stat-number" style="color: #dc3545;">{results['failed']}</div>
                        <div>Failed</div>
                    </div>
                    ''', unsafe_allow_html=True)

                with col_stat5:
                    st.markdown(f'''
                    <div class="stat-box">
                        <div class="stat-number">{results['processing_time']}s</div>
                        <div>Processing Time</div>
                    </div>
                    ''', unsafe_allow_html=True)

                st.markdown('</div>', unsafe_allow_html=True)
        
            # Success summary
            processed_count = results['successful'] + results.get('skipped', 0)
            if processed_count > 0:
                st.markdown(f'''
                <div class="result-success">
                    <h4>✅ Processing Completed!</h4>
                    <p><strong>New records processed:</strong> {results['successful']}/{results['total_records']}</p>
                    <p><strong>Skipped (already exist):</strong> {results.get('skipped', 0)}/{results['total_records']}</p>
                    <p><strong>S3 Folder:</strong> {results['s3_folder']}</p>
                    <p><strong>Processing Time:</strong> {results['processing_time']} seconds</p>
                </div>
                ''', unsafe_allow_html=True)

                # Show successful items
                if results['success_items']:
                    with st.expander(f"✅ View New Items ({len(results['success_items'])})"):
                        for item in results['success_items']:
                            st.write(f"• {item}")

            # Show skipped items
            if results.get('skipped_items'):
                st.markdown(f'''
                <div class="result-warning">
                    <h4>⏭️ Skipped Items ({len(results['skipped_items'])})</h4>
                    <p>These images already exist in the database and were skipped:</p>
                </div>
                ''', unsafe_allow_html=True)

                with st.expander(f"⏭️ View Skipped Items ({len(results['skipped_items'])})"):
                    for item in results['skipped_items']:
                        st.write(f"• {item}")
        
            # Errors and warnings
            if results['errors']:
                st.markdown(f'''
                <div class="result-warning">
                    <h4>⚠️ Issues Found ({len(results['errors'])})</h4>
                    <p>Some items could not be processed. See details below:</p>
                </div>
                ''', unsafe_allow_html=True)

                with st.expander(f"⚠️ View Issues ({len(results['errors'])})"):
                    for error in results['errors']:
                        st.write(f"• {error}")

            # Complete failure case
            if results['successful'] == 0 and results.get('skipped', 0) == 0 and results['total_records'] == 0:
                st.markdown('''
                <div class="result-error">
                    <h4>❌ Processing Failed</h4>
                    <p>No records could be processed. Please check your files and try again.</p>
                </div>
                ''', unsafe_allow_html=True)

# Tab 2: Review
with tab2:
    st.markdown("### 🔍 Review Pending Items")

    # Initialize session state
    if 'selected_image' not in st.session_state:
        st.session_state.selected_image = None
    if 'search_term' not in st.session_state:
        st.session_state.search_term = ""
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 1

    # Load pending review data
    try:
        conn = get_db_connection()
        pending_items = get_pending_review_items(conn)
        conn.close()

        # Display statistics
        if pending_items:
            # Calculate statistics
            total_items = len(pending_items)
            items_with_comments = len([item for item in pending_items if item.get('review_comment')])
            compliance_pass = len([item for item in pending_items if item.get('compliance_assessment')])
            compliance_fail = total_items - compliance_pass

            # Display stats in columns
            stat_col1, stat_col2, stat_col3, stat_col4 = st.columns(4)

            with stat_col1:
                st.markdown(f"""
                <div class="stat-box">
                    <div class="stat-number">{total_items}</div>
                    <div>Total Items</div>
                </div>
                """, unsafe_allow_html=True)

            with stat_col2:
                st.markdown(f"""
                <div class="stat-box">
                    <div class="stat-number" style="color: #28a745;">{compliance_pass}</div>
                    <div>Pass</div>
                </div>
                """, unsafe_allow_html=True)

            with stat_col3:
                st.markdown(f"""
                <div class="stat-box">
                    <div class="stat-number" style="color: #dc3545;">{compliance_fail}</div>
                    <div>Fail</div>
                </div>
                """, unsafe_allow_html=True)

            with stat_col4:
                st.markdown(f"""
                <div class="stat-box">
                    <div class="stat-number" style="color: #6c757d;">{items_with_comments}</div>
                    <div>With Comments</div>
                </div>
                """, unsafe_allow_html=True)

            st.markdown("---")
        else:
            st.info("📝 No pending review items found")
            st.stop()

    except Exception as e:
        st.error(f"❌ Error loading pending review data: {str(e)}")
        st.stop()
    
    # Apply search filter
    search_term = st.session_state.search_term
    if search_term:
        filtered_items = [item for item in pending_items if search_term.lower() in item['image_name'].lower()]
    else:
        filtered_items = pending_items
    
    # Pagination setup
    items_per_page = 20
    total_items = len(filtered_items)
    total_pages = max(1, (total_items - 1) // items_per_page + 1)
    
    # Ensure current page is valid
    if st.session_state.current_page > total_pages:
        st.session_state.current_page = 1
    
    # Calculate pagination
    start_idx = (st.session_state.current_page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, total_items)
    page_items = filtered_items[start_idx:end_idx] if filtered_items else []
    
    # 3-Column Layout
    col1, col2, col3 = st.columns([0.25, 0.35, 0.40])
    
    # Column 1: Image List
    with col1:
        st.markdown("#### 📷 Pending Images")
        st.caption(f"Total: {len(pending_items)} | Filtered: {total_items}")
        
        # Search box
        new_search = st.text_input(
            "🔍 Search", 
            value=st.session_state.search_term,
            placeholder="Enter image name...",
            key="search_input"
        )
        
        # Update search term if changed
        if new_search != st.session_state.search_term:
            st.session_state.search_term = new_search
            st.session_state.current_page = 1
            st.rerun()
        
        # Pagination controls
        if total_pages > 1:
            col_prev, col_page, col_next = st.columns([1, 2, 1])
            
            with col_prev:
                if st.button("◀", disabled=st.session_state.current_page <= 1):
                    st.session_state.current_page -= 1
                    st.rerun()
            
            with col_page:
                st.markdown(f"**Page {st.session_state.current_page}/{total_pages}**")
            
            with col_next:
                if st.button("▶", disabled=st.session_state.current_page >= total_pages):
                    st.session_state.current_page += 1
                    st.rerun()
        
        # Image list
        st.markdown("---")

        if page_items:
            for item in page_items:
                image_name = item['image_name']
                compliance = item['compliance_assessment']
                has_comment = bool(item.get('review_comment'))

                # Check if selected
                is_selected = st.session_state.selected_image == image_name

                # Create status indicators
                compliance_icon = "✅" if compliance else "❌"
                comment_icon = "💬" if has_comment else ""

                # Create clickable button with conditional styling
                if is_selected:
                    st.markdown(f"""
                    <div style="background-color: #2196f3; color: white; padding: 8px 12px;
                                margin: 2px 0; border-radius: 5px; border: 2px solid #1976d2;">
                        <strong>📷 {image_name}</strong><br>
                        <small>{compliance_icon} {comment_icon}</small>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    # Create button with status indicators
                    button_text = f"📷 {image_name}\n{compliance_icon} {comment_icon}"
                    if st.button(button_text, key=f"btn_{image_name}", use_container_width=True):
                        st.session_state.selected_image = image_name
                        st.rerun()
        else:
            st.info("No items found")
        
        # Show current range
        if page_items:
            st.caption(f"Showing {start_idx + 1}-{end_idx} of {total_items}")
    
    # Column 2: Image Display
    with col2:
        st.markdown("#### 🖼️ Image Preview")

        if st.session_state.selected_image:
            # Find selected item data
            selected_item = next((item for item in filtered_items
                                if item['image_name'] == st.session_state.selected_image), None)

            if selected_item:
                st.markdown(f"**📷 {selected_item['image_name']}**")

                # Display timestamp
                if selected_item['timestamp']:
                    st.caption(f"📅 {selected_item['timestamp']}")

                try:
                    # Display from S3 URL using presigned URL
                    if selected_item['s3_url']:
                        # Generate presigned URL for secure access
                        presigned_url = generate_presigned_url(selected_item['s3_url'])
                        st.image(presigned_url, width=300, caption="Image from S3")
                    else:
                        st.error("❌ No S3 URL available for this image")

                except Exception as e:
                    st.error(f"❌ Cannot display image: {str(e)}")
                    st.info("💡 Please check if the S3 URL is accessible or if AWS credentials are configured correctly")

            else:
                st.info("🔍 Selected image not found in current filter")
        else:
            st.markdown("""
            <div style="height: 300px; display: flex; align-items: center; justify-content: center;
                        background-color: #f0f0f0; border: 2px dashed #ccc; border-radius: 10px;">
                <p style="color: #666; text-align: center;">
                    📷 Select an image from the list<br>to view details
                </p>
            </div>
            """, unsafe_allow_html=True)
    
    # Column 3: Analysis Results
    with col3:
        st.markdown("#### 📊 Analysis Results")

        if st.session_state.selected_image:
            # Find selected item data
            selected_item = next((item for item in filtered_items
                                if item['image_name'] == st.session_state.selected_image), None)

            if selected_item:
                # Display compliance status
                compliance = selected_item['compliance_assessment']
                compliance_text = "✅ Pass" if compliance else "❌ Fail"
                compliance_color = "#28a745" if compliance else "#dc3545"

                st.markdown(f"""
                <div style="background-color: {'#d4edda' if compliance else '#f8d7da'};
                            padding: 10px; border-radius: 5px; margin-bottom: 15px;
                            border: 1px solid {'#c3e6cb' if compliance else '#f5c6cb'};">
                    <strong>🎯 Compliance Status:</strong><br>
                    <span style="color: {compliance_color}; font-weight: bold; font-size: 1.1em;">{compliance_text}</span>
                </div>
                """, unsafe_allow_html=True)

                # Display review comment if exists
                if selected_item.get('review_comment'):
                    st.markdown("#### 💬 Review Comment")
                    st.markdown(f"""
                    <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;
                                margin-bottom: 15px; border-left: 4px solid #007bff;">
                        {selected_item['review_comment']}
                    </div>
                    """, unsafe_allow_html=True)

                # Display product analysis
                if selected_item['product_count']:
                    try:
                        # Parse JSON product count data
                        if isinstance(selected_item['product_count'], str):
                            product_data = json.loads(selected_item['product_count'])
                        else:
                            product_data = selected_item['product_count']

                        # Display shelves analysis based on new format
                        if 'shelves' in product_data:
                            shelves = product_data['shelves']
                            total_shelves = len(shelves)

                            st.markdown(f"""
                            <div style="background-color: #e8f5e8; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                                <strong>🗄️ Shelf Analysis</strong><br>
                                Total Shelves: <strong>{total_shelves}</strong>
                            </div>
                            """, unsafe_allow_html=True)

                            # Create shelves table
                            shelves_data = []
                            total_joco = 0
                            total_abben = 0
                            total_boncha = 0

                            for shelf in shelves:
                                shelf_number = shelf.get('shelf_number', 'N/A')
                                drinks = shelf.get('drinks', {})

                                joco_count = drinks.get('joco', 0)
                                abben_count = drinks.get('abben', 0)
                                boncha_count = drinks.get('boncha', 0)

                                shelves_data.append({
                                    'Shelf': f"Shelf {shelf_number}",
                                    'Joco': joco_count,
                                    'Abben': abben_count,
                                    'Boncha': boncha_count,
                                    'Total': joco_count + abben_count + boncha_count
                                })

                                total_joco += joco_count
                                total_abben += abben_count
                                total_boncha += boncha_count

                            # Display as DataFrame table
                            df_shelves = pd.DataFrame(shelves_data)
                            st.dataframe(df_shelves, use_container_width=True, hide_index=True)

                            # # Display summary
                            # total_products = total_joco + total_abben + total_boncha
                            # st.markdown(f"""
                            # <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa;
                            #             border-radius: 5px; font-size: 14px;">
                            #     📈 <strong>Summary:</strong><br>
                            #     • Total Products: <strong>{total_products}</strong><br>
                            #     • Joco: <strong>{total_joco}</strong><br>
                            #     • Abben: <strong>{total_abben}</strong><br>
                            #     • Boncha: <strong>{total_boncha}</strong><br>
                            #     • Across <strong>{total_shelves}</strong> shelves
                            # </div>
                            # """, unsafe_allow_html=True)
                        else:
                            st.warning("⚠️ No shelves data found in the expected format")

                        # Show raw JSON data in expandable section
                        with st.expander("📄 View Raw JSON Data"):
                            st.json(product_data)

                    except json.JSONDecodeError as e:
                        st.error(f"❌ Invalid JSON format: {str(e)}")

                        # Show raw data
                        with st.expander("📄 Raw Data"):
                            st.text(str(selected_item['product_count']))

                    except Exception as e:
                        st.error(f"❌ Error parsing product data: {str(e)}")

                        # Show raw data as fallback
                        with st.expander("📄 Raw Data"):
                            st.text(str(selected_item['product_count']))
                else:
                    st.warning("⚠️ No product analysis data available")
        else:
            st.markdown("""
            <div style="height: 200px; display: flex; align-items: center; justify-content: center;
                        background-color: #fff3cd; border: 2px dashed #ffc107; border-radius: 10px;">
                <p style="color: #856404; text-align: center;">
                    📊 Select an image to view<br>analysis results
                </p>
            </div>
            """, unsafe_allow_html=True)

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; padding: 20px;">
    <p>🔧 Configure settings in <code>config.py</code> | 📝 Check console logs for detailed information</p>
</div>
""", unsafe_allow_html=True)# =============================================================================
# CONFIGURATION FILE - EASY TO EDIT
# =============================================================================

# S3 Configuration
S3_BUCKET_NAME = ""  # CHANGE THIS
S3_FOLDER_PREFIX = ""  # Folder naming: RAG_Update_<timestamp>
S3_REGION = ""

# PostgreSQL Database Configuration
DB_CONFIG = {
    "host": "",
    "port": 5432,
    "database": "",
    "user": "",      # CHANGE THIS
    "password": "",  # CHANGE THIS
}

# Table Configuration
DB_TABLE = ""

# AWS Bedrock Configuration
BEDROCK_REGION = ""  # Region for Titan embedding model
EMBEDDING_MODEL = ""
EMBEDDING_DIMENSION = 1024

# File Processing Configuration
SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg']
MAX_FILE_SIZE_MB = 200
CSV_ENCODING = 'utf-8'

# Logging Configuration
LOG_LEVEL = "INFO"import boto3
import psycopg2
import json
import base64
import logging
from datetime import datetime
from typing import List, Dict, Tuple
from PIL import Image
from io import BytesIO
import tempfile
import os

from config import (
    S3_BUCKET_NAME, S3_FOLDER_PREFIX, S3_REGION,
    DB_CONFIG, DB_TABLE,
    BEDROCK_REGION, EMBEDDING_MODEL, EMBEDDING_DIMENSION
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize AWS clients
s3_client = boto3.client('s3', region_name=S3_REGION)
bedrock_client = boto3.client('bedrock-runtime', region_name=BEDROCK_REGION)

def create_s3_folder() -> str:
    """
    Create timestamped S3 folder
    
    Returns:
        folder_path: S3 folder path
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_path = f"{S3_FOLDER_PREFIX}_{timestamp}/"
    
    logger.info(f"Created S3 folder path: {folder_path}")
    return folder_path

def upload_file_to_s3(file_obj, folder_path: str, filename: str) -> str:
    """
    Upload file to S3
    
    Returns:
        s3_url: Full S3 URL
    """
    try:
        s3_key = f"{folder_path}{filename}"
        
        # Reset file pointer
        file_obj.seek(0)
        
        # Upload to S3
        s3_client.upload_fileobj(
            file_obj, 
            S3_BUCKET_NAME, 
            s3_key,
            ExtraArgs={'ContentType': 'application/octet-stream'}
        )
        
        s3_url = f"https://{S3_BUCKET_NAME}.s3.{S3_REGION}.amazonaws.com/{s3_key}"
        logger.info(f"✅ Uploaded to S3: {filename}")
        
        return s3_url
        
    except Exception as e:
        logger.error(f"❌ S3 upload failed for {filename}: {str(e)}")
        raise

def generate_image_embedding(image_bytes: bytes) -> Tuple[List[float], str]:
    """
    Generate embedding for image using AWS Bedrock Titan
    
    Returns:
        (embedding_vector, base64_image)
    """
    try:
        # Convert to base64
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        # Prepare request payload
        payload = {
            "inputImage": base64_image,
            "embeddingConfig": {
                "outputEmbeddingLength": EMBEDDING_DIMENSION
            }
        }
        
        # Call Bedrock API
        response = bedrock_client.invoke_model(
            body=json.dumps(payload),
            modelId=EMBEDDING_MODEL,
            accept="application/json",
            contentType="application/json"
        )
        
        # Parse response
        response_body = json.loads(response.get('body').read())
        embedding = response_body.get('embedding')
        
        if not embedding:
            raise ValueError("No embedding returned from Bedrock")
        
        logger.info(f"✅ Generated embedding: {len(embedding)} dimensions")
        return embedding, base64_image
        
    except Exception as e:
        logger.error(f"❌ Embedding generation failed: {str(e)}")
        raise

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        logger.info("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        raise

def check_image_exists_in_db(conn, image_name: str) -> bool:
    """
    Check if an image name already exists in the database

    Args:
        conn: Database connection
        image_name: Name of the image to check

    Returns:
        exists: Boolean indicating if image name exists
    """
    try:
        cursor = conn.cursor()

        check_query = f"""
            SELECT COUNT(*) FROM {DB_TABLE} WHERE image_name = %s
        """

        cursor.execute(check_query, (image_name,))
        count = cursor.fetchone()[0]
        cursor.close()

        exists = count > 0
        if exists:
            logger.info(f"🔍 Image already exists in DB: {image_name}")

        return exists

    except Exception as e:
        logger.error(f"❌ DB check failed for {image_name}: {str(e)}")
        return False

def insert_record_to_db(conn, record: Dict) -> bool:
    """
    Insert record to PostgreSQL database

    Args:
        conn: Database connection
        record: Dict with keys: image_name, embedding, image_base64, description, s3_url

    Returns:
        success: Boolean
    """
    try:
        cursor = conn.cursor()

        insert_query = f"""
            INSERT INTO {DB_TABLE} (image_name, embedding, image_base64, description, s3_url)
            VALUES (%s, %s, %s, %s, %s)
        """

        cursor.execute(insert_query, (
            record['image_name'],
            record['embedding'],
            record['image_base64'],
            record['description'],
            record['s3_url']
        ))

        conn.commit()
        cursor.close()

        logger.info(f"✅ Inserted to DB: {record['image_name']}")
        return True

    except Exception as e:
        logger.error(f"❌ DB insertion failed for {record['image_name']}: {str(e)}")
        conn.rollback()
        return False

def process_image_record(record: Dict, s3_folder: str) -> Tuple[bool, str]:
    """
    Process single image record: generate embedding, upload to S3, insert to DB
    Skips processing if image name already exists in database

    Args:
        record: Dict with image_name, description, image_file
        s3_folder: S3 folder path

    Returns:
        (success, error_message)
    """
    try:
        image_name = record['image_name']
        description = record['description']
        image_file = record['image_file']

        logger.info(f"🔄 Processing: {image_name}")

        # Check if image already exists in database
        conn = get_db_connection()
        if check_image_exists_in_db(conn, image_name):
            conn.close()
            logger.info(f"⏭️ Skipping {image_name}: Already exists in database")
            return True, "Skipped - Already exists"

        # Get image bytes
        image_file.seek(0)
        image_bytes = image_file.getvalue()

        # Generate embedding
        embedding, base64_image = generate_image_embedding(image_bytes)

        # Upload image to S3
        image_filename = f"{image_name}.jpg"
        image_file.seek(0)  # Reset pointer
        s3_url = upload_file_to_s3(image_file, s3_folder, image_filename)

        # Prepare database record
        db_record = {
            'image_name': image_name,
            'embedding': embedding,
            'image_base64': base64_image,
            'description': description,
            's3_url': s3_url
        }

        # Insert to database
        success = insert_record_to_db(conn, db_record)
        conn.close()

        if success:
            logger.info(f"✅ Successfully processed: {image_name}")
            return True, "Success"
        else:
            return False, "Database insertion failed"

    except Exception as e:
        error_msg = f"Processing failed for {record.get('image_name', 'unknown')}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return False, error_msg

def upload_csv_to_s3(csv_file, s3_folder: str, filename: str = "descriptions.csv") -> str:
    """
    Upload CSV file to S3

    Returns:
        s3_url: Full S3 URL for CSV
    """
    try:
        csv_file.seek(0)  # Reset file pointer
        s3_url = upload_file_to_s3(csv_file, s3_folder, filename)
        logger.info(f"✅ CSV uploaded to S3: {filename}")
        return s3_url

    except Exception as e:
        logger.error(f"❌ CSV upload to S3 failed: {str(e)}")
        raise

def get_pending_review_items(conn) -> List[Dict]:
    """
    Get all pending review items from results table

    Args:
        conn: Database connection

    Returns:
        List of dictionaries containing pending review data
    """
    try:
        cursor = conn.cursor()

        query = """
            SELECT id, image_name, s3_url, product_count, compliance_assessment, review_comment, timestamp
            FROM results
            WHERE need_review = true
            ORDER BY timestamp DESC
        """
        cursor.execute(query)

        pending_items = []
        for row in cursor.fetchall():
            pending_items.append({
                'id': row[0],
                'image_name': row[1],
                's3_url': row[2],
                'product_count': row[3],
                'compliance_assessment': row[4],
                'review_comment': row[5] or '',
                'timestamp': row[6]
            })

        cursor.close()
        logger.info(f"🔍 Found {len(pending_items)} pending review items")
        return pending_items

    except Exception as e:
        logger.error(f"❌ Failed to get pending review items: {str(e)}")
        return []

def generate_presigned_url(s3_url: str, expiration: int = 3600) -> str:
    """
    Generate presigned URL for S3 object

    Args:
        s3_url: Original S3 URL
        expiration: URL expiration time in seconds (default: 1 hour)

    Returns:
        presigned_url: Presigned URL for accessing the S3 object
    """
    try:
        # Extract bucket and key from S3 URL
        # Format: https://bucket-name.s3.region.amazonaws.com/key
        if not s3_url or not s3_url.startswith('https://'):
            return s3_url

        # Parse S3 URL to extract bucket and key
        url_parts = s3_url.replace('https://', '').split('/')
        bucket_part = url_parts[0]  # bucket-name.s3.region.amazonaws.com
        key = '/'.join(url_parts[1:])  # path/to/object

        # Extract bucket name (everything before .s3.)
        bucket_name = bucket_part.split('.s3.')[0]

        # Generate presigned URL
        presigned_url = s3_client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket_name, 'Key': key},
            ExpiresIn=expiration
        )

        logger.info(f"✅ Generated presigned URL for: {key}")
        return presigned_url

    except Exception as e:
        logger.error(f"❌ Failed to generate presigned URL for {s3_url}: {str(e)}")
        # Return original URL as fallback
        return s3_url

        #!/bin/bash

IMAGE_NAME="review-ui"

if [ -n "$1" ]; then
    ver="$1"
    IMAGE_TAG="$1"
    if docker images "$IMAGE_NAME:$IMAGE_TAG" | grep -q "$IMAGE_TAG"; then
        echo "Image $IMAGE_NAME:$IMAGE_TAG exists locally."
        exit
    fi
else
    echo "Please provide a version number in \$1"
    exit
fi

# Build và tag image
docker build -t $IMAGE_NAME:$IMAGE_TAG .
docker tag $IMAGE_NAME:$IMAGE_TAG $IMAGE_NAME:latest

# Stop và remove container cũ
docker stop $IMAGE_NAME
docker rm $IMAGE_NAME

# Sử dụng environment variables cho AWS credentials
docker run -dt -p 8081:8080 \
  -e AWS_ACCESS_KEY_ID=$(aws configure get aws_access_key_id) \
  -e AWS_SECRET_ACCESS_KEY=$(aws configure get aws_secret_access_key) \
  -e AWS_SESSION_TOKEN=$(aws configure get aws_session_token) \
  -e AWS_REGION=$(aws configure get region) \
  --name $IMAGE_NAME $IMAGE_NAME

sleep 1
docker logs $IMAGE_NAMEimport logging
from typing import List, Dict, Tuple
import time

from utils import validate_csv_file, validate_images, match_csv_with_images
from data_ops import create_s3_folder, upload_csv_to_s3, process_image_record

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RAGProcessor:
    """Main processor for RAG data ingestion workflow"""
    
    def __init__(self):
        self.processing_results = {
            'total_records': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'processing_time': 0,
            'errors': [],
            'success_items': [],
            'skipped_items': [],
            's3_folder': '',
            'csv_s3_url': ''
        }
    
    def validate_inputs(self, csv_file, uploaded_images) -> Tuple[bool, str]:
        """
        Validate CSV and images inputs
        
        Returns:
            (is_valid, error_message)
        """
        logger.info("🔍 Starting input validation...")
        
        # Validate CSV
        csv_valid, csv_error, self.df = validate_csv_file(csv_file)
        if not csv_valid:
            return False, f"CSV Error: {csv_error}"
        
        # Validate Images
        images_valid, images_error, self.validated_images = validate_images(uploaded_images)
        if not images_valid:
            return False, f"Images Error: {images_error}"
        
        logger.info("✅ Input validation completed successfully")
        return True, "Validation passed"
    
    def match_data(self) -> Tuple[bool, str]:
        """
        Match CSV records with images
        
        Returns:
            (has_matches, status_message)
        """
        logger.info("🔗 Starting CSV-Image matching...")
        
        self.matched_records, self.missing_items = match_csv_with_images(
            self.df, self.validated_images
        )
        
        if not self.matched_records:
            return False, "No matching records found between CSV and images"
        
        # Store missing items in results
        self.processing_results['errors'].extend(self.missing_items)
        
        logger.info(f"✅ Matching completed: {len(self.matched_records)} records ready for processing")
        return True, f"Found {len(self.matched_records)} matching records"
    
    def setup_s3_storage(self, csv_file) -> Tuple[bool, str]:
        """
        Create S3 folder and upload CSV
        
        Returns:
            (success, status_message)
        """
        try:
            logger.info("☁️ Setting up S3 storage...")
            
            # Create timestamped S3 folder
            self.s3_folder = create_s3_folder()
            self.processing_results['s3_folder'] = self.s3_folder
            
            # Upload CSV to S3
            csv_s3_url = upload_csv_to_s3(csv_file, self.s3_folder)
            self.processing_results['csv_s3_url'] = csv_s3_url
            
            logger.info(f"✅ S3 setup completed: {self.s3_folder}")
            return True, f"S3 folder created: {self.s3_folder}"
            
        except Exception as e:
            error_msg = f"S3 setup failed: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return False, error_msg
    
    def process_records(self) -> Tuple[bool, str]: 
        """
        Process all matched records: embeddings + S3 + DB
        
        Returns:
            (success, status_message)
        """
        start_time = time.time()
        logger.info(f"🚀 Starting processing of {len(self.matched_records)} records...")
        
        self.processing_results['total_records'] = len(self.matched_records)
        
        for i, record in enumerate(self.matched_records, 1):
            logger.info(f"Processing record {i}/{len(self.matched_records)}: {record['image_name']}")
            
            try:
                success, error_msg = process_image_record(record, self.s3_folder)

                if success:
                    if error_msg == "Skipped - Already exists":
                        self.processing_results['skipped'] += 1
                        self.processing_results['skipped_items'].append(record['image_name'])
                        logger.info(f"⏭️ [{i}/{len(self.matched_records)}] Skipped: {record['image_name']} (already exists)")
                    else:
                        self.processing_results['successful'] += 1
                        self.processing_results['success_items'].append(record['image_name'])
                        logger.info(f"✅ [{i}/{len(self.matched_records)}] Success: {record['image_name']}")
                else:
                    self.processing_results['failed'] += 1
                    self.processing_results['errors'].append(f"{record['image_name']}: {error_msg}")
                    logger.error(f"❌ [{i}/{len(self.matched_records)}] Failed: {record['image_name']} - {error_msg}")

            except Exception as e:
                self.processing_results['failed'] += 1
                error_msg = f"{record['image_name']}: Unexpected error - {str(e)}"
                self.processing_results['errors'].append(error_msg)
                logger.error(f"❌ [{i}/{len(self.matched_records)}] Exception: {error_msg}")
        
        # Calculate processing time
        self.processing_results['processing_time'] = round(time.time() - start_time, 2)
        
        # Determine overall success
        processed_count = self.processing_results['successful'] + self.processing_results['skipped']
        if processed_count > 0:
            success_msg = f"Processing completed: {self.processing_results['successful']} new, {self.processing_results['skipped']} skipped, {self.processing_results['failed']} failed"
            logger.info(f"✅ {success_msg}")
            return True, success_msg
        else:
            failure_msg = "Processing failed: No records were processed successfully"
            logger.error(f"❌ {failure_msg}")
            return False, failure_msg
    
    def run_full_workflow(self, csv_file, uploaded_images) -> Dict:
        """
        Run complete RAG data ingestion workflow
        
        Returns:
            processing_results: Dict with complete results
        """
        logger.info("🎯 Starting RAG Data Ingestion Workflow...")
        
        try:
            # Step 1: Validate inputs
            valid, error = self.validate_inputs(csv_file, uploaded_images)
            if not valid:
                self.processing_results['errors'].append(f"Validation Error: {error}")
                return self.processing_results
            
            # Step 2: Match CSV with images
            has_matches, match_msg = self.match_data()
            if not has_matches:
                self.processing_results['errors'].append(f"Matching Error: {match_msg}")
                return self.processing_results
            
            # Step 3: Setup S3 storage
            s3_success, s3_msg = self.setup_s3_storage(csv_file)
            if not s3_success:
                self.processing_results['errors'].append(f"S3 Error: {s3_msg}")
                return self.processing_results
            
            # Step 4: Process all records
            process_success, process_msg = self.process_records()
            
            # Final summary
            logger.info("🏁 RAG Data Ingestion Workflow Completed!")
            logger.info(f"📊 Results Summary:")
            logger.info(f"   Total Records: {self.processing_results['total_records']}")
            logger.info(f"   Successful: {self.processing_results['successful']}")
            logger.info(f"   Skipped: {self.processing_results['skipped']}")
            logger.info(f"   Failed: {self.processing_results['failed']}")
            logger.info(f"   Processing Time: {self.processing_results['processing_time']}s")
            logger.info(f"   S3 Folder: {self.processing_results['s3_folder']}")
            
            return self.processing_results
            
        except Exception as e:
            error_msg = f"Workflow failed with unexpected error: {str(e)}"
            logger.error(f"❌ {error_msg}")
            self.processing_results['errors'].append(error_msg)
            return self.processing_resultsstreamlit==1.42.0
boto3==1.35.0
psycopg2-binary==2.9.9
pandas==2.2.0
Pillow>=10.2.0
python-dotenv==1.0.0import pandas as pd
import logging
from typing import List, Dict, Tuple
import os
from config import SUPPORTED_IMAGE_FORMATS, MAX_FILE_SIZE_MB, CSV_ENCODING

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_csv_file(csv_file) -> Tuple[bool, str, pd.DataFrame]:
    """
    Validate CSV file and return DataFrame
    
    Returns:
        (is_valid, error_message, dataframe)
    """
    try:
        # Check file size
        if csv_file.size > MAX_FILE_SIZE_MB * 1024 * 1024:
            return False, f"CSV file too large. Max size: {MAX_FILE_SIZE_MB}MB", None
        
        # Read CSV
        csv_file.seek(0)  # Reset file pointer
        df = pd.read_csv(csv_file, encoding=CSV_ENCODING)
        
        # Check required columns
        required_columns = ['image_name', 'value']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            return False, f"Missing required columns: {missing_columns}", None
        
        # Check for empty DataFrame
        if df.empty:
            return False, "CSV file is empty", None
        
        # Log CSV info
        logger.info(f"CSV validated successfully: {len(df)} records found")
        
        return True, "Valid", df
        
    except Exception as e:
        logger.error(f"CSV validation error: {str(e)}")
        return False, f"CSV validation error: {str(e)}", None

def validate_images(uploaded_images) -> Tuple[bool, str, List]:
    """
    Validate uploaded images
    
    Returns:
        (is_valid, error_message, validated_images)
    """
    try:
        if not uploaded_images:
            return False, "No images uploaded", []
        
        validated_images = []
        errors = []
        
        for img in uploaded_images:
            # Check file extension
            file_ext = img.name.lower().split('.')[-1]
            if file_ext not in SUPPORTED_IMAGE_FORMATS:
                errors.append(f"{img.name}: Unsupported format. Use: {SUPPORTED_IMAGE_FORMATS}")
                continue
            
            # Check file size
            if img.size > MAX_FILE_SIZE_MB * 1024 * 1024:
                errors.append(f"{img.name}: File too large. Max: {MAX_FILE_SIZE_MB}MB")
                continue
            
            validated_images.append(img)
        
        if errors:
            error_msg = "Image validation errors:\n" + "\n".join(errors)
            logger.warning(error_msg)
            return len(validated_images) > 0, error_msg, validated_images
        
        logger.info(f"Images validated successfully: {len(validated_images)} images")
        return True, "Valid", validated_images
        
    except Exception as e:
        logger.error(f"Image validation error: {str(e)}")
        return False, f"Image validation error: {str(e)}", []

def match_csv_with_images(df: pd.DataFrame, uploaded_images: List) -> Tuple[List[Dict], List[str]]:
    """
    Match CSV records with uploaded images
    
    Returns:
        (matched_records, missing_items_log)
    """
    matched_records = []
    missing_items = []
    
    # Create image lookup dictionary
    image_lookup = {}
    for img in uploaded_images:
        # Extract image name without extension
        img_name = img.name.replace('.jpg', '').replace('.jpeg', '').replace('.png', '')
        image_lookup[img_name] = img
    
    logger.info(f"Processing CSV records: {len(df)} total")
    logger.info(f"Available images: {len(image_lookup)} total")
    
    # Process each CSV record
    for index, row in df.iterrows():
        image_name = str(row['image_name']).strip()
        description = str(row['value']).strip()
        
        # Check if corresponding image exists
        if image_name in image_lookup:
            matched_records.append({
                'image_name': image_name,
                'description': description,
                'image_file': image_lookup[image_name]
            })
            logger.info(f"✅ Matched: {image_name}")
        else:
            missing_msg = f"❌ Missing image for CSV record: {image_name}"
            missing_items.append(missing_msg)
            logger.warning(missing_msg)
    
    # Check for images without CSV records
    for img_name in image_lookup.keys():
        if not any(record['image_name'] == img_name for record in matched_records):
            missing_msg = f"❌ Missing CSV record for image: {img_name}.jpg"
            missing_items.append(missing_msg)
            logger.warning(missing_msg)
    
    logger.info(f"✅ Successfully matched: {len(matched_records)} records")
    logger.info(f"❌ Missing items: {len(missing_items)}")
    
    return matched_records, missing_items

def detect_image_format(image_bytes) -> str:
    """Detect image format from binary data"""
    if image_bytes.startswith(b'\xff\xd8\xff'):
        return 'jpeg'
    elif image_bytes.startswith(b'\x89PNG\r\n\x1a\n'):
        return 'png'
    else:
        return 'jpeg'  # Default fallback